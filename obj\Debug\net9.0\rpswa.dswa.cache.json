{"GlobalPropertiesHash": "6eskvwBnC0x8M9QQ1TBLOjcA4B6d5yFuXVta/V51ePQ=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["+vE1h6xNfr3TZ2Tbw/EunYTXm4qg3J3q6HQ9Tyq5ab4=", "DF9McYgEzkiqlPsJ6uITlsrZESBR6FoHod+GzTOuZcA=", "t7U8oVEq/QZ++4DnniO+lugGpYHSr3MoHmI/9NTubOU=", "K6y/6LuF+dsCL969W9Ebdat4vx1zVoURt7S+UxU1KR0=", "lhP7JBSzj8KxCyf0KCkSEdOwJCddn7Uoyrj/EsHKnLE=", "euWPdYEv4TeMXcBk9+W6bXPizBIp4EZNRun1+z0VF8Y=", "+I2V5GG5D+TMMGSV3qb1hkeYepvGo1aOtEE0h13EL1k=", "+s5+XTIRvsgCUFBozeDK7M4opPglLucy98J0oN/33b8=", "CIv5th317NbrqBxnFwRmoEnxyHAcsL4EP4XqR8F+kJw=", "CbTbI8NEJ4ho8eIwZYvPSbqLqxcMrIIsCWWiMwmn9No=", "SII+TJjtXtPCeau5JUoyxPANgP3mYeEaHeOXtbF+l70=", "V2cPGCimPvXLrcw0D+XFM6sEddQEXMo1+FY8rhdo4QM=", "kgUY3VD0wB9wafyzF7JaeMQDiNb0TblW5D9O6WNgLeM=", "GLm7mgCnPLAR3PJALLFjXbAgRCf2YLATwElIB5jJghE=", "RNOJzL0JKq7v2PUuiXE/m6k+4/5elYPxRG3/MoZPzAI=", "/djlLvqjPY6G44h8uxRU7S9whbb7EYWgis/7phIPCL4=", "3BFiHLk9u3yKq9Q+qbKfpu4UXqAQDtkorSoxW4W0+TM=", "lYIBpEWgH8sPg0CoogLa9lk7jxN1t0FBy8ayRS6U8Zc=", "CHQfnkbpseLRaKyLDAICGD94jY1tg6lzb5D/YQpgjuw=", "0vWtFrAHwnudEIJ5TiEZIBkQLArOT5CUBSiEjrqVi+A=", "fa2ixVhXLS6Q26Z1RR3Oe2phgDPpxdlXBbRXOBZ+Hh0=", "Gn48JTR0GzoKhK6NnztutzNVI7ZDhXi76CHdEkB6jzg=", "MC+k7IReJB1Bab768aWm92qEJU9Xond9gOUbnm2OedY=", "gqHbht8/Eye5LCo/HXCmdy26Bjf2F2r36Xcw0Ym/3qM=", "bUIQcVGilccuetitppDgofgTvSzPHGOJ7zHsP4wzax8=", "AHfy8jtNrG+ML8tsor0nhthmW4agFDO8X9t1PleK1rI=", "ykX5BXAxdanNKuTka9mTX15t03OsCjs/wG5juUcQDVg=", "0Aea89wGrJ/YxYhkL2GeUqNQRJJzC57fhwl1ct2WoZk=", "lwHjjemH/DIzvlF6IPSkCZzv8tkmEa4zUPHA0u339Mc=", "C0uuUvBjemIa53hhVin9aJmfWdOdvNRKD3Du5KL2b1I=", "nhIAYFv2yC6uyTBIRtBQeqEgVp8hFC0JvbCyTvQOnho=", "rcYGV+O+RSWaQizSvtePyanqirkRo1AH07K1UvhjldA=", "3XVtHip0qkvr9WHnULHcojypBCyyeZDtzzbAOPfWA7M=", "bYh+5vWXVtIn5yTeusBXLhLbq8dF3BO3z/Gv3tT7ntY=", "fFNAzZ9ds7TkhjHTUPzcgbd7vszaN7RUXBqUD3johFs=", "lX90BWuk/TVeNc6Hou78JqQIEi0cTIrsfXtS6BZPPsk=", "UJEUaCk8HGnJZoLERkRIk6ws4z655PizpG///i7IyMY=", "NrXWvw38D0SA6ZlAaS12rWWHpnR1Hd5qBzDsrpLx5pw=", "YvF0bnhIhVLrZWl4whfNrap+ZaVadbEIvTRYw4NRrjs=", "ewcnZ4WurGuirZ82RTsFC8PL+ueeYhQuekMZUa/W4uc=", "UEA9PGSGgJ1pR6dmKm+fSBY9zoneK5uCVKdFfbuQyeM=", "uJOKCgkDpVwI0/rYpf2seD9BVxRR/TA3pNUaPc4clVY=", "XVeepMXlok1+9kr+PJWyzgjpHZcF4CnDeemMB2D18RY=", "O6w9/7PfkM7JFNC0qjfj2iLITR5sah+bLhfrgwt/6wk=", "PpAnUoG74KIpfWvBmclqiMFmrj7V6Mpptna46EDlaXg=", "eKcTS6PrXUsnLLQsazKY0nCdOXPLDZcd+rosib02es0=", "WyerQCNgsAqYiqJBI6I1WfUI4/pmgoq5GLIZCDCkeaE=", "8sUD0JEMzhLnE6MnSBOkty8gOgP6NKVYtjMseezRCzk=", "BFrMHHG9UUGjTk7NRiT687FLnNMf2fN8prxdqr+YAvA=", "6B5pJfP4Wdcw78PKuUqzZltpFRoPyoIoHEvQcFyyT+U=", "RSg1Lz7ljtrXqiAgUcVHbfTDkanS9wxbqCfbOO2IkCg=", "NuvtX6tQgzoG0c3sh4uGuLnyOzPRMORrHbwJyuICJjU=", "OMeqCs6bp9e3xezQcNRH7zA1XKF41tS4s/cbSPf4PDk=", "laITDXIkoBIf7afh4UIXjwubqawF4H2gkQrujwOLoNQ=", "arOqJP6BgLmeVr0sIpzahVMxI/W6XXGKrW2Hfn0nfWo=", "ArjfKu63KXkSQJ/lOxfaJdE/m02pQpDNqHnJUgC9BbM=", "6J2BAXpy5kyw0H/YK7CjX0O5iNyCGctXwAqBQ5GxFnU=", "OzeLanvMMmwJKFL3MY9pV99pDfvrcu1f9tjvZ9ykJds=", "pw5+djBlVsQxTJcJCUs/wwdRdt6jyc+K/jXGdfcKODs=", "L762JTXCI2i8XPHHDn04ovis14Jv+bVCr+Hl412cC3U=", "zVCgYA97i8Ab6H1wbIt/SeuNe77dB6xEOIXcTCEloGk=", "RfwWT1y26j+PX/y736dYQsEf1PVDqIEXUZ8ngHmQK0I=", "/vgIxtqpihoZZsHbcp4ZR1Az3HhyH/rfBKYAthlohRY=", "6+QO4v8zwpHdIjrPXx1vEh4vDPauGYKtaKYLbL+ixL8=", "o70igQ8+8Zq/cIJR1v5zXVaIoxBR7LdTXwWCN0SmVbQ=", "f9AMVjSTRO/1hdvjymVKPS72tE+IjZVmgcACMSdCd9I=", "3KLFYlEaLDU/7M67yh85e9KGY4ynhQiiJd+JB8uM59M=", "t+eLpt50EoDCvOraR6P8F4GzCmppWI58OguRoZAqatU=", "f9DOZRlrop/s0HG5izEZO2FLEbR7iLEfm3wnIXE6nD0=", "TT1py45f39lkRI5wP+8O2PchcDfUq0pGoP0Q36WjuTY=", "lGVaeeY8Q8shYTW50JzuK/1B8LCDWhzGh6wnA/0e7Ko=", "gKttybYUxdUXapnd1lBCffcfe5d7hxmqQgG1NRnF5Ag=", "sswRbUP5MyOa6x9SizWCfAS7cvvWyhFnnPcx76x2VS0="], "CachedAssets": {"+vE1h6xNfr3TZ2Tbw/EunYTXm4qg3J3q6HQ9Tyq5ab4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\css\\site.css", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b9sayid5wm", "Integrity": "j6fhJSuuyLpOSLuPJU0TsDV0iNjor5S3rDnvxJrt4bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 667, "LastWriteTime": "2025-06-30T13:55:53.7357455+00:00"}, "DF9McYgEzkiqlPsJ6uITlsrZESBR6FoHod+GzTOuZcA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\favicon.ico", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-06-30T13:55:53.6323179+00:00"}, "t7U8oVEq/QZ++4DnniO+lugGpYHSr3MoHmI/9NTubOU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\js\\site.js", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-06-30T13:55:53.7378026+00:00"}, "K6y/6LuF+dsCL969W9Ebdat4vx1zVoURt7S+UxU1KR0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-06-30T13:55:53.4665623+00:00"}, "lhP7JBSzj8KxCyf0KCkSEdOwJCddn7Uoyrj/EsHKnLE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-06-30T13:55:53.4711752+00:00"}, "euWPdYEv4TeMXcBk9+W6bXPizBIp4EZNRun1+z0VF8Y=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-06-30T13:55:53.4732635+00:00"}, "+I2V5GG5D+TMMGSV3qb1hkeYepvGo1aOtEE0h13EL1k=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-06-30T13:55:53.4743982+00:00"}, "+s5+XTIRvsgCUFBozeDK7M4opPglLucy98J0oN/33b8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-06-30T13:55:53.4754095+00:00"}, "CIv5th317NbrqBxnFwRmoEnxyHAcsL4EP4XqR8F+kJw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-06-30T13:55:53.4769972+00:00"}, "CbTbI8NEJ4ho8eIwZYvPSbqLqxcMrIIsCWWiMwmn9No=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-06-30T13:55:53.4775092+00:00"}, "SII+TJjtXtPCeau5JUoyxPANgP3mYeEaHeOXtbF+l70=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-06-30T13:55:53.4785197+00:00"}, "V2cPGCimPvXLrcw0D+XFM6sEddQEXMo1+FY8rhdo4QM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-06-30T13:55:53.4785197+00:00"}, "kgUY3VD0wB9wafyzF7JaeMQDiNb0TblW5D9O6WNgLeM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-06-30T13:55:53.4795231+00:00"}, "GLm7mgCnPLAR3PJALLFjXbAgRCf2YLATwElIB5jJghE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-06-30T13:55:53.4805208+00:00"}, "RNOJzL0JKq7v2PUuiXE/m6k+4/5elYPxRG3/MoZPzAI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-06-30T13:55:53.4815213+00:00"}, "/djlLvqjPY6G44h8uxRU7S9whbb7EYWgis/7phIPCL4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-06-30T13:55:53.4815213+00:00"}, "3BFiHLk9u3yKq9Q+qbKfpu4UXqAQDtkorSoxW4W0+TM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-06-30T13:55:53.4835788+00:00"}, "lYIBpEWgH8sPg0CoogLa9lk7jxN1t0FBy8ayRS6U8Zc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-06-30T13:55:53.4835788+00:00"}, "CHQfnkbpseLRaKyLDAICGD94jY1tg6lzb5D/YQpgjuw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-06-30T13:55:53.4845788+00:00"}, "0vWtFrAHwnudEIJ5TiEZIBkQLArOT5CUBSiEjrqVi+A=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-06-30T13:55:53.4855785+00:00"}, "fa2ixVhXLS6Q26Z1RR3Oe2phgDPpxdlXBbRXOBZ+Hh0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-06-30T13:55:53.4876425+00:00"}, "Gn48JTR0GzoKhK6NnztutzNVI7ZDhXi76CHdEkB6jzg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-06-30T13:55:53.4896566+00:00"}, "MC+k7IReJB1Bab768aWm92qEJU9Xond9gOUbnm2OedY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-06-30T13:55:53.4906593+00:00"}, "gqHbht8/Eye5LCo/HXCmdy26Bjf2F2r36Xcw0Ym/3qM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-06-30T13:55:53.49166+00:00"}, "bUIQcVGilccuetitppDgofgTvSzPHGOJ7zHsP4wzax8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-06-30T13:55:53.4926609+00:00"}, "AHfy8jtNrG+ML8tsor0nhthmW4agFDO8X9t1PleK1rI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-06-30T13:55:53.4936571+00:00"}, "ykX5BXAxdanNKuTka9mTX15t03OsCjs/wG5juUcQDVg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-06-30T13:55:53.494657+00:00"}, "0Aea89wGrJ/YxYhkL2GeUqNQRJJzC57fhwl1ct2WoZk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-06-30T13:55:53.4967206+00:00"}, "lwHjjemH/DIzvlF6IPSkCZzv8tkmEa4zUPHA0u339Mc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-06-30T13:55:53.5027173+00:00"}, "C0uuUvBjemIa53hhVin9aJmfWdOdvNRKD3Du5KL2b1I=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-06-30T13:55:53.5049763+00:00"}, "nhIAYFv2yC6uyTBIRtBQeqEgVp8hFC0JvbCyTvQOnho=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-06-30T13:55:53.5113942+00:00"}, "rcYGV+O+RSWaQizSvtePyanqirkRo1AH07K1UvhjldA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-06-30T13:55:53.5137549+00:00"}, "3XVtHip0qkvr9WHnULHcojypBCyyeZDtzzbAOPfWA7M=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-06-30T13:55:53.5194482+00:00"}, "bYh+5vWXVtIn5yTeusBXLhLbq8dF3BO3z/Gv3tT7ntY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-06-30T13:55:53.5225449+00:00"}, "fFNAzZ9ds7TkhjHTUPzcgbd7vszaN7RUXBqUD3johFs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-06-30T13:55:53.5273262+00:00"}, "lX90BWuk/TVeNc6Hou78JqQIEi0cTIrsfXtS6BZPPsk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-06-30T13:55:53.5340527+00:00"}, "UJEUaCk8HGnJZoLERkRIk6ws4z655PizpG///i7IyMY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-06-30T13:55:53.5401762+00:00"}, "NrXWvw38D0SA6ZlAaS12rWWHpnR1Hd5qBzDsrpLx5pw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-06-30T13:55:53.5421758+00:00"}, "YvF0bnhIhVLrZWl4whfNrap+ZaVadbEIvTRYw4NRrjs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-06-30T13:55:53.5464426+00:00"}, "ewcnZ4WurGuirZ82RTsFC8PL+ueeYhQuekMZUa/W4uc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-06-30T13:55:53.548462+00:00"}, "UEA9PGSGgJ1pR6dmKm+fSBY9zoneK5uCVKdFfbuQyeM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-06-30T13:55:53.5539801+00:00"}, "uJOKCgkDpVwI0/rYpf2seD9BVxRR/TA3pNUaPc4clVY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-06-30T13:55:53.5562624+00:00"}, "XVeepMXlok1+9kr+PJWyzgjpHZcF4CnDeemMB2D18RY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-06-30T13:55:53.5605831+00:00"}, "O6w9/7PfkM7JFNC0qjfj2iLITR5sah+bLhfrgwt/6wk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-06-30T13:55:53.562582+00:00"}, "PpAnUoG74KIpfWvBmclqiMFmrj7V6Mpptna46EDlaXg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-06-30T13:55:53.5675812+00:00"}, "eKcTS6PrXUsnLLQsazKY0nCdOXPLDZcd+rosib02es0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-06-30T13:55:53.5695852+00:00"}, "WyerQCNgsAqYiqJBI6I1WfUI4/pmgoq5GLIZCDCkeaE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-06-30T13:55:53.5726233+00:00"}, "8sUD0JEMzhLnE6MnSBOkty8gOgP6NKVYtjMseezRCzk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-06-30T13:55:53.5147715+00:00"}, "BFrMHHG9UUGjTk7NRiT687FLnNMf2fN8prxdqr+YAvA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-06-30T13:55:53.6373185+00:00"}, "6B5pJfP4Wdcw78PKuUqzZltpFRoPyoIoHEvQcFyyT+U=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-06-30T13:55:53.6423183+00:00"}, "RSg1Lz7ljtrXqiAgUcVHbfTDkanS9wxbqCfbOO2IkCg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-06-30T13:55:53.5303897+00:00"}, "NuvtX6tQgzoG0c3sh4uGuLnyOzPRMORrHbwJyuICJjU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-06-30T13:55:53.6293176+00:00"}, "OMeqCs6bp9e3xezQcNRH7zA1XKF41tS4s/cbSPf4PDk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-06-30T13:55:53.6313177+00:00"}, "laITDXIkoBIf7afh4UIXjwubqawF4H2gkQrujwOLoNQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-06-30T13:55:53.6343178+00:00"}, "arOqJP6BgLmeVr0sIpzahVMxI/W6XXGKrW2Hfn0nfWo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-06-30T13:55:53.6353186+00:00"}, "ArjfKu63KXkSQJ/lOxfaJdE/m02pQpDNqHnJUgC9BbM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-06-30T13:55:53.5235559+00:00"}, "6J2BAXpy5kyw0H/YK7CjX0O5iNyCGctXwAqBQ5GxFnU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 285314, "LastWriteTime": "2025-06-30T13:55:53.6000569+00:00"}, "OzeLanvMMmwJKFL3MY9pV99pDfvrcu1f9tjvZ9ykJds=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-06-30T13:55:53.6022699+00:00"}, "pw5+djBlVsQxTJcJCUs/wwdRdt6jyc+K/jXGdfcKODs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-06-30T13:55:53.6095371+00:00"}, "L762JTXCI2i8XPHHDn04ovis14Jv+bVCr+Hl412cC3U=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-06-30T13:55:53.6142228+00:00"}, "zVCgYA97i8Ab6H1wbIt/SeuNe77dB6xEOIXcTCEloGk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-06-30T13:55:53.6172346+00:00"}, "RfwWT1y26j+PX/y736dYQsEf1PVDqIEXUZ8ngHmQK0I=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-06-30T13:55:53.6262353+00:00"}, "/vgIxtqpihoZZsHbcp4ZR1Az3HhyH/rfBKYAthlohRY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "Hotel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Hotel\\Hotel\\wwwroot\\", "BasePath": "_content/Hotel", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-06-30T13:55:53.5204561+00:00"}}, "CachedCopyCandidates": {}}